FROM mcr.microsoft.com/dotnet/sdk:9.0 AS build
WORKDIR /src

# Copia os arquivos de projeto e dependências
COPY ["src/Api/Api.csproj", "src/Api/"]
COPY ["src/Application/Application.csproj", "src/Application/"]
COPY ["src/Infrastructure/Infrastructure.csproj", "src/Infrastructure/"]
COPY ["src/Domain/Domain.csproj", "src/Domain/"]
COPY ["Directory.Build.props", "."]
COPY ["Directory.Packages.props", "."]
COPY ["global.json", "."]
COPY ["NuGet.config", "."]

# Configura credenciais do NuGet temporariamente
ARG NUGET_USERNAME
ARG NUGET_TOKEN
RUN dotnet nuget update source github --username "$NUGET_USERNAME" --password "$NUGET_TOKEN" --store-password-in-clear-text;

# Restaura dependências
RUN dotnet restore "src/Api/Api.csproj"

# Copia todo o código fonte
COPY . .

# Builda o projeto
WORKDIR /src/src/Api
RUN dotnet build Api.csproj -c Release

# Publica a aplicação
RUN dotnet publish "Api.csproj" -c Release -o /app/publish /p:UseAppHost=false /p:SkipNSwag=True

FROM mcr.microsoft.com/dotnet/aspnet:9.0 AS runtime
WORKDIR /app
EXPOSE 8080

# Copia arquivos publicados
COPY --from=build /app/publish .

# Variáveis de ambiente
ENV ASPNETCORE_URLS=http://+:8080
ENV ASPNETCORE_ENVIRONMENT=Development

# Aplica migrations e inicia o serviço
ENTRYPOINT ["dotnet", "HighCopy.Service.dll"]
