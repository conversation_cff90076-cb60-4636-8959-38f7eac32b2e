using Application;
using Infrastructure;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container
builder.Services.AddControllers();
builder.Services.AddOpenApi();

// Add layers following Clean Architecture
builder.Services.AddInfrastructureServices("HighCopyServiceDb");
builder.Services.AddApplicationServices();

var app = builder.Build();

// Configure the HTTP request pipeline
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.UseHttpsRedirection();
}

app.UseHttpsRedirection();
app.MapControllers();

app.Run();

public partial class Program { }