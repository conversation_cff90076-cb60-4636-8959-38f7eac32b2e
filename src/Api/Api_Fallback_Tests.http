### Teste do Sistema de Fallback ChatGPT -> Gemini

### 1. Teste básico com ChatGPT (padrão)
POST http://localhost:5000/api/content/generate
Content-Type: application/json

{
  "prompt": "Explique o que é Clean Architecture em uma frase"
}

### 2. Teste forçando ChatGPT especificamente
POST http://localhost:5000/api/content/generate
Content-Type: application/json

{
  "prompt": "Como funciona dependency injection?",
  "preferredProvider": "chatgpt"
}

### 3. Teste forçando Gemini especificamente  
POST http://localhost:5000/api/content/generate
Content-Type: application/json

{
  "prompt": "O que são os princípios SOLID?",
  "preferredProvider": "gemini"
}

### 4. Verificar provedores disponíveis
GET http://localhost:5000/api/content/providers

### 5. Health Check dos provedores
GET http://localhost:5000/api/content/health

### 6. Teste de fallback (simular falha do ChatGPT)
POST http://localhost:5000/api/content/generate
Content-Type: application/json

{
  "prompt": "Este prompt testará o fallback automático do ChatGPT para Gemini",
  "preferredProvider": "chatgpt"
}

### 7. Teste com prompt vazio (deve retornar erro de validação)
POST http://localhost:5000/api/content/generate
Content-Type: application/json

{
  "prompt": "",
  "preferredProvider": "chatgpt"
}

### 8. Teste com provedor inválido (deve usar fallback para padrão)
POST http://localhost:5000/api/content/generate
Content-Type: application/json

{
  "prompt": "Teste com provedor inexistente",
  "preferredProvider": "provedor-inexistente"
}

### 9. Teste com prompt null (deve retornar erro de validação)
POST http://localhost:5000/api/content/generate
Content-Type: application/json

{
  "preferredProvider": "chatgpt"
}

### 10. Teste análise de arquivo (necessita arquivo)
# POST http://localhost:5000/api/content/analyze-file
# Content-Type: multipart/form-data; boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW
# 
# ------WebKitFormBoundary7MA4YWxkTrZu0gW
# Content-Disposition: form-data; name="file"; filename="test.txt"
# Content-Type: text/plain
# 
# Este é um arquivo de teste para análise.
# Contém informações importantes sobre o projeto.
# ------WebKitFormBoundary7MA4YWxkTrZu0gW
# Content-Disposition: form-data; name="provider"
# 
# gemini
# ------WebKitFormBoundary7MA4YWxkTrZu0gW--

### 11. Teste análise de arquivo sem arquivo (deve retornar erro)
POST http://localhost:5000/api/content/analyze-file
Content-Type: multipart/form-data

### 12. Teste de geração de conteúdo com prompt muito longo
POST http://localhost:5000/api/content/generate
Content-Type: application/json

{
  "prompt": "Este é um prompt muito longo para testar como o sistema lida com prompts extensos. Quero que você explique detalhadamente como funcionam os microserviços, suas vantagens e desvantagens, quando usar, como implementar, as melhores práticas, padrões de design, problemas comuns e suas soluções.",
  "preferredProvider": "gemini"
}
