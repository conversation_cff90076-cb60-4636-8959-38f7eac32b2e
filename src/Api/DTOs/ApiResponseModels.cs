namespace Api.DTOs;

public class GenerateContentRequest
{
    public string Prompt { get; set; } = string.Empty;
    public string? PreferredProvider { get; set; }
}

public class GenerateContentResponse
{
    public bool Success { get; set; }
    public string Content { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? Provider { get; set; }
}

public class SetupTemplateResponse
{
    public bool Success { get; set; }
    public string Template { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public string? EntityType { get; set; }
    public string? Identifier { get; set; }
}

public class FileAnalysisResponse
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string? FileName { get; set; }
    public long? FileSize { get; set; }
    public string? FileType { get; set; }
    public string? Analysis { get; set; }
    public string? Provider { get; set; }
}
