using Domain.Interfaces.Services;
using Microsoft.AspNetCore.Mvc;
using Api.DTOs;

namespace Api.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ContentController(IContentService contentService, ILogger<ContentController> logger) : ControllerBase
{
    [HttpPost("generate")]
    public async Task<IActionResult> GenerateContent([FromBody] GenerateContentRequest request)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(request.Prompt))
            {
                return BadRequest(new GenerateContentResponse
                {
                    Success = false,
                    Message = "Prompt cannot be empty"
                });
            }

            logger.LogInformation("Content generation request received with provider: {Provider}", 
                request.PreferredProvider ?? "chatgpt (default)");
            
            var result = await contentService.GenerateContentAsync(request.Prompt, request.PreferredProvider);
            
            return Ok(new GenerateContentResponse
            {
                Content = result,
                Success = true,
                Message = "Content generated successfully",
                Provider = request.PreferredProvider ?? "default"
            });
        }
        catch (ArgumentException ex)
        {
            logger.LogWarning("Invalid argument: {Message}", ex.Message);
            return BadRequest(new GenerateContentResponse
            {
                Success = false,
                Message = ex.Message
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Error generating content");
            return StatusCode(500, new GenerateContentResponse
            {
                Success = false,
                Message = "An error occurred while generating content"
            });
        }
    }

    [HttpPost("analyze-file")]
    public async Task<IActionResult> AnalyzeFile(IFormFile file, [FromForm] string? provider = null)
    {
        try
        {
            if (file == null || file.Length == 0)
            {
                return BadRequest(new FileAnalysisResponse
                {
                    Success = false,
                    Message = "No file was uploaded"
                });
            }

            var analysis = await contentService.AnalyzeFileAsync(file);

            return Ok(new FileAnalysisResponse
            {
                Success = true,
                Message = "File analyzed successfully",
                FileName = file.FileName,
                FileSize = file.Length,
                Analysis = analysis,
                Provider = provider ?? "default"
            });
        }
        catch (NotSupportedException ex)
        {
            logger.LogWarning(ex, "File type not supported: {FileName}", file.FileName);
            return BadRequest(new FileAnalysisResponse
            {
                Success = false,
                Message = ex.Message,
                FileName = file.FileName
            });
        }
        catch (InvalidOperationException ex)
        {
            logger.LogError(ex, "Error processing file: {FileName}", file.FileName);
            return StatusCode(500, new FileAnalysisResponse
            {
                Success = false,
                Message = ex.Message,
                FileName = file.FileName
            });
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Unexpected error analyzing file: {FileName}", file.FileName);
            return StatusCode(500, new FileAnalysisResponse
            {
                Success = false,
                Message = "An unexpected error occurred while analyzing the file",
                FileName = file.FileName
            });
        }
    }

    [HttpGet("providers")]
    public IActionResult GetProviders()
    {
        return Ok(new[]
        {
            new { Name = "chatgpt", DisplayName = "ChatGPT (OpenAI)", IsDefault = true },
            new { Name = "gemini", DisplayName = "Gemini (Google)", IsDefault = false }
        });
    }

    [HttpGet("health")]
    public IActionResult GetHealth()
    {
        return Ok(new
        {
            Status = "Healthy",
            Service = "Content API",
            Version = "1.0.0",
            CheckedAt = DateTime.UtcNow
        });
    }
}
