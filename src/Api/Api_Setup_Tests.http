### Testes dos Endpoints de Setup - Clean Architecture

### 1. Teste Expert Setup - Gerar template
GET http://localhost:5000/api/expert-setup/1/template
Content-Type: application/json

### 2. Teste Expert Setup - Gerar template com tipo específico
GET http://localhost:5000/api/expert-setup/expert-123/template?expertType=marketing
Content-Type: application/json

### 3. Teste Expert Setup - Buscar expert por ID
GET http://localhost:5000/api/expert-setup/1
Content-Type: application/json

### 4. Teste Expert Setup - Buscar expert por External ID
GET http://localhost:5000/api/expert-setup/expert-ext-456
Content-Type: application/json

### 5. Teste Campaign Setup - Gerar template básico
GET http://localhost:5000/api/campaign-setup/1/template
Content-Type: application/json

### 6. Teste Campaign Setup - Gerar template com relacionamentos
GET http://localhost:5000/api/campaign-setup/campaign-123/template?expertId=1&productId=2&publicId=3
Content-Type: application/json

### 7. Teste Campaign Setup - Buscar campaign
GET http://localhost:5000/api/campaign-setup/campaign-456
Content-Type: application/json

### 8. Teste Product Setup - Gerar template
GET http://localhost:5000/api/product-setup/product-789/template
Content-Type: application/json

### 9. Teste Product Setup - Buscar product
GET http://localhost:5000/api/product-setup/1
Content-Type: application/json

### 10. Teste Public Setup - Gerar template
GET http://localhost:5000/api/public-setup/public-abc/template
Content-Type: application/json

### 11. Teste Public Setup - Buscar public
GET http://localhost:5000/api/public-setup/2
Content-Type: application/json

### 12. Teste Expert Setup - ID inexistente (deve retornar 404)
GET http://localhost:5000/api/expert-setup/999999
Content-Type: application/json

### 13. Teste Campaign Setup - Template com IDs inexistentes
GET http://localhost:5000/api/campaign-setup/999999/template?expertId=999&productId=999&publicId=999
Content-Type: application/json
