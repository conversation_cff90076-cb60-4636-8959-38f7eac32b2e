using Domain.Entities;

namespace Domain.Interfaces.Services;

public interface ITemplateService
{
    Task<string> ProcessExpertTemplateAsync(Expert expert, string? expertType = null);
    Task<string> ProcessCampaignTemplateAsync(Campaign campaign, Expert? expert = null, Product? product = null, Public? publicEntity = null);
    Task<string> ProcessProductTemplateAsync(Product product);
    Task<string> ProcessPublicTemplateAsync(Public publicEntity);
}
