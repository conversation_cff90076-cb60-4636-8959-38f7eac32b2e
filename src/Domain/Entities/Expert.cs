using HighCapital.Core.Services;

namespace Domain.Entities;

public sealed class Expert
{
    public int Id { get; set; }
    public string ExternalId { get; set; } = IdGenService.GetId();
    
    public required string Name { get; set; }
    
    public required string AreaOfExpertise { get; set; }
    
    public required string Biography { get; set; }

    public required string Moment { get; set; }

    public required string Dolor { get; set; }
    
    public required string Credibility { get; set; }

    public required string Recognition { get; set; }

    public required string TrackRecord { get; set; }
    
    public required string HowProductWorks { get; set; }

    public required string VoicePersonality { get; set; }

    public required string EssentialValues { get; set; }

    public required string Enterprise { get; set; }
}