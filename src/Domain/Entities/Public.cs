using HighCapital.Core.Services;

namespace Domain.Entities;

public class Public
{
  public int Id { get; set; }
  public string  ExternalId { get; set; } = IdGenService.GetId();
  
  public required string Name { get; set; }
  public required string Local { get; set; }
  public required string FamilySituation { get; set; }
  public required string Personality { get; set; }
  public required string Hobbies { get; set; }
  public required string Lifestyle { get; set; }
  public required string PersonalValue { get; set; }
  public required string Roof { get; set; }
  public required string NextLevel { get; set; }
  public required string DropOfWater { get; set; }
  public required string Beliefs { get; set; }
  public required string SelfVisions { get; set; }
  public required string PossibleObjections { get; set; }
  public required string OwnCommunication { get; set; }
}