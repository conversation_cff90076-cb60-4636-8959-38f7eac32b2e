using Domain.Enums.Products;
using HighCapital.Core.Services;

namespace Domain.Entities;

public sealed class Product
{
     public int Id { get; set; }
     
     public  string ExternalId { get; set; } = IdGenService.GetId();
     
     public required string Name { get; set; }

     public required string Benefits { get; set; }

     public required string SocialProof { get; set; }

     public required string Metodology { get; set; }

     public required string Guarantee { get; set; }

     public required ProductDeliverablesEnum Deliverables  { get; set; }
     
     public required ProductCustomerJourneyEnum CustomerJourney { get; set; }
}