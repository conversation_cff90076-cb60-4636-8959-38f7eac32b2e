using Domain.Enums.Campaing;
using HighCapital.Core.Services;

namespace Domain.Entities; 

public sealed class Campaign 
{
    public int Id {get; set;}
    public string ExternalId { get; set; } = IdGenService.GetId();
    
    public required string Name {get; set;}
    
    public required string Ideia { get; set; }

    public required string Emotion { get; set; }

    public required string Belief { get; set; }
    
    public required CampaingTypeEnum Type { get; set; }
                            
    public required CampaingConscienceLevelEnum ConscienceLevel { get; set; }
    
    public required CampaingSophisticationLevelEnum SophisticationLevel { get; set; }
} 