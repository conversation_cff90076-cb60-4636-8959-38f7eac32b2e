using Domain.Entities;
using Domain.Interfaces.Repositories;
using Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure;

public class ExpertRepository(ApplicationDbContext context) : IExpertRepository
{
    public async Task<Expert> GetByIdAsync(int id)
    {
        return await context.Experts.FindAsync(id) ?? throw new KeyNotFoundException($"Expert with Id {id} not found.");
    }

    public async Task<Expert> GetByExternalIdAsync(string externalId)
    {
        return await context.Experts.FirstOrDefaultAsync(e => e.ExternalId == externalId) ?? throw new KeyNotFoundException($"Expert with ExternalId {externalId} not found.");
    }

    public async Task<IEnumerable<Expert>> GetAllAsync()
    {
        return await context.Experts.AsNoTracking().ToListAsync();
    }

    public async Task AddAsync(Expert expert)
    {
        await context.Experts.AddAsync(expert);
    }

    public async Task UpdateAsync(Expert expert)
    {
        context.Experts.Update(expert);
        await Task.CompletedTask;
    }

    public async Task DeleteAsync(int id)
    {
        var expert = await context.Experts.FindAsync(id) 
            ?? throw new KeyNotFoundException($"Expert with Id {id} not found.");
        context.Experts.Remove(expert);
    }

    public async Task SaveChangesAsync()
    {
        await context.SaveChangesAsync();
    }
}