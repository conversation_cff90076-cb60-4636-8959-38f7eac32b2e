using Domain.Entities;
using Domain.Interfaces.Repositories;
using Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Data.Repositories;

public class CampaingRepository(ApplicationDbContext context) : ICampaignRepository
{
    public async Task<Campaign> GetByIdAsync(int id)
    {
        return await context.Campaigns.FindAsync(id) ?? throw new KeyNotFoundException($"Campaign with Id {id} not found.");
    }

    public async Task<Campaign> GetByExternalIdAsync(string externalId)
    {
        return await context.Campaigns.FirstOrDefaultAsync(c => c.ExternalId == externalId) ?? throw new KeyNotFoundException($"Campaign with ExternalId {externalId} not found.");
    }

    public async Task<IEnumerable<Campaign>> GetAllAsync()
    {
        return await context.Campaigns.AsNoTracking().ToListAsync();
    }

    public async Task UpdateAsync(Campaign campaign)
    {
        context.Campaigns.Update(campaign);
        await Task.CompletedTask;
    }

    public async Task AddAsync(Campaign campaign)
    {
        await context.Campaigns.AddAsync(campaign);
    }

    public async Task DeleteAsync(int id)
    {
        var campaing = await context.Campaigns.FindAsync(id) 
            ?? throw new KeyNotFoundException($"Campaign with Id {id} not found.");
        context.Campaigns.Remove(campaing);
    }

    public async Task SaveChangesAsync()
    {
        await context.SaveChangesAsync();
    }
}