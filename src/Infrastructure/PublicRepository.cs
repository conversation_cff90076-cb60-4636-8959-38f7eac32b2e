using Domain.Entities;
using Domain.Interfaces.Repositories;
using Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure;

public class PublicRepository(ApplicationDbContext context) : IPublicRepository
{
    public async Task<Public> GetByIdAsync(int id)
    {
        return await context.Publics.FindAsync(id)
            ?? throw new KeyNotFoundException($"Public with Id {id} not found.");
    }

    public async Task<Public> GetByExternalIdAsync(string externalId)
    {
        return await context.Publics.FirstOrDefaultAsync(p => p.ExternalId == externalId) 
            ?? throw new KeyNotFoundException($"Public with ExternalId {externalId} not found.");
    }

    public async Task<IEnumerable<Public>> GetAllAsync()
    {
        return await context.Publics.AsNoTracking().ToListAsync();
    }

    public async Task AddAsync(Public publicEntity)
    {
        await context.Publics.AddAsync(publicEntity);
    }

    public async Task UpdateAsync(Public publicEntity)
    {
        context.Publics.Update(publicEntity);
        await Task.CompletedTask;
    }

    public async Task DeleteAsync(int id)
    {
        var publicEntity = await context.Publics.FindAsync(id)
            ?? throw new KeyNotFoundException($"Public with Id {id} not found.");
        context.Publics.Remove(publicEntity);
    }

    public async Task SaveChangesAsync()
    {
        await context.SaveChangesAsync();
    }
}