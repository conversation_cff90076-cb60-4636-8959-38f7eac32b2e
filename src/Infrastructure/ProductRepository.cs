using Domain.Entities;
using Domain.Interfaces.Repositories;
using Infrastructure.Database;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure;

public class ProductRepository(ApplicationDbContext context) : IProductRepository
{
    public async Task<Product> GetByIdAsync(int id)
    {
        return await context.Products.FindAsync(id)
            ?? throw new KeyNotFoundException($"Product with Id {id} not found.");
    }

    public async Task<Product> GetByExternalIdAsync(string externalId)
    {
        return await context.Products.FirstOrDefaultAsync(p => p.ExternalId == externalId) 
            ?? throw new KeyNotFoundException($"Product with ExternalId {externalId} not found.");
    }

    public async Task<IEnumerable<Product>> GetAllAsync()
    {
        return await context.Products.AsNoTracking().ToListAsync();
    }

    public async Task AddAsync(Product product)
    {
        await context.Products.AddAsync(product);
    }

    public async Task UpdateAsync(Product product)
    {
        context.Products.Update(product);
        await Task.CompletedTask;
    }

    public async Task DeleteAsync(int id)
    {
        var product = await context.Products.FindAsync(id)
            ?? throw new KeyNotFoundException($"Product with Id {id} not found.");
        context.Products.Remove(product);
    }

    public async Task SaveChangesAsync()
    {
        await context.SaveChangesAsync();
    }
}