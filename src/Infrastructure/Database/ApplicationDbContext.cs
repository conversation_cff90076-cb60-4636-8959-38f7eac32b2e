﻿using Domain.Entities;
using Microsoft.EntityFrameworkCore;

namespace Infrastructure.Database;

public class ApplicationDbContext(DbContextOptions<ApplicationDbContext> options) : DbContext(options)
{
    public DbSet<Campaign> Campaigns { get; set; }
    public DbSet<Public> Publics { get; set; }
    public DbSet<Product> Products { get; set; }
    public DbSet<Expert> Experts { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        
        modelBuilder.Entity<Campaign>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.ExternalId).IsUnique();
            entity.Property(e => e.Type).HasConversion<int>();
            entity.Property(e => e.ConscienceLevel).HasConversion<int>();
            entity.Property(e => e.SophisticationLevel).HasConversion<int>();
        });

        
        modelBuilder.Entity<Product>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.ExternalId).IsUnique();
            entity.Property(e => e.Deliverables).HasConversion<int>();
            entity.Property(e => e.CustomerJourney).HasConversion<int>();
        });

       
        modelBuilder.Entity<Public>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.ExternalId).IsUnique();
        });
        
        modelBuilder.Entity<Expert>(entity =>
        {
            entity.HasKey(e => e.Id);
            entity.HasIndex(e => e.ExternalId).IsUnique();
        });

        base.OnModelCreating(modelBuilder);
    }
}