using Domain.Interfaces.Repositories;
using Infrastructure.Database;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Infrastructure;

public static class DependencyInjection
{
    public static IServiceCollection AddInfrastructureServices(this IServiceCollection services, string connectionString)
    {
        // Database
        services.AddDbContext<ApplicationDbContext>(options =>
            options.UseInMemoryDatabase(connectionString));
        
        // Repositories
        services.AddScoped<IExpertRepository, ExpertRepository>();
        services.AddScoped<ICampaignRepository, CampaingRepository>();
        services.AddScoped<IProductRepository, ProductRepository>();
        services.AddScoped<IPublicRepository, PublicRepository>();
        
        return services;
    }
}
