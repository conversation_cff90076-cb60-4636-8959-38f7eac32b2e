using Domain.Interfaces;
using Domain.Interfaces.Services;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;

namespace Application.Services;

public class ContentService(
    IAiOrchestrator aiOrchestrator,
    IFileReaderService fileReaderService,
    ILogger<ContentService> logger) : IContentService
{
    public async Task<string> GenerateContentAsync(string prompt, string? preferredProvider = null)
    {
        logger.LogInformation("Generating content for prompt with preferred provider: {PreferredProvider}", preferredProvider ?? "default");
        return await aiOrchestrator.GenerateContentAsync(prompt, preferredProvider);
    }

    public async Task<string> AnalyzeFileAsync(IFormFile file)
    {
        logger.LogInformation("Analyzing file: {FileName}", file.FileName);
        
        if (!fileReaderService.IsFileTypeSupported(file.FileName))
        {
            var supportedExtensions = string.Join(", ", fileReaderService.GetSupportedExtensions());
            throw new NotSupportedException($"File type not supported. Supported extensions: {supportedExtensions}");
        }

        string fileContent;
        using (var stream = file.OpenReadStream())
        {
            fileContent = await fileReaderService.ReadFileAsync(stream, file.FileName);
        }

        if (string.IsNullOrWhiteSpace(fileContent))
        {
            throw new InvalidOperationException("Could not extract content from the file or file is empty.");
        }

        var prompt = $@"Analyze the following file and provide a detailed explanation about:
1. What type of file this is
2. What is the main content
3. What could be the purpose or use of this file
4. Summary of the most important data/information

File name: {file.FileName}
Size: {file.Length} bytes

File content:
{fileContent}

Please provide a clear and structured analysis in English.";

        return await aiOrchestrator.GenerateContentAsync(prompt);
    }
}
