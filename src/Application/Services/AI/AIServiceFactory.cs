using Domain.Interfaces.Services;
using Microsoft.Extensions.DependencyInjection;

namespace Application.Services.AI;

public interface IAiServiceFactory
{
    IGenerativeAiService CreateService(string providerName);
}

public class AiServiceFactory(IServiceProvider serviceProvider) : IAiServiceFactory
{
    private readonly IServiceProvider _serviceProvider = serviceProvider ?? throw new ArgumentNullException(nameof(serviceProvider));

    public IGenerativeAiService CreateService(string providerName)
    {
        return providerName.ToLowerInvariant() switch
        {
            "chatgpt" or "openai" => _serviceProvider.GetRequiredService<ChatGptService>(),
            "gemini" or "google" => _serviceProvider.GetRequiredService<GeminiService>(),
            _ => throw new NotSupportedException($"AI provider '{providerName}' is not supported.")
        };
    }
}
