using System.Text;
using System.Text.Json;
using Application.Models.AI;
using Domain.Interfaces.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Application.Services.AI;

public class ChatGptService : IGenerativeAiService
{
    private readonly HttpClient _httpClient;
    private readonly string _apiKey;
    private readonly ILogger<ChatGptService> _logger;
    private const string BaseUrl = "https://api.openai.com/v1/chat/completions";

    public ChatGptService(HttpClient httpClient, IConfiguration configuration, ILogger<ChatGptService> logger)
    {
        _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        
        _apiKey = configuration.GetConnectionString("OPENAI_API_KEY")
                  ?? throw new ArgumentException("OPENAI_API_KEY not configured in appsettings.json.");
        
        _httpClient.Timeout = TimeSpan.FromSeconds(30);
    }

    public async Task<string> GenerateAsync(string prompt)
    {
        if (string.IsNullOrWhiteSpace(prompt))
            throw new ArgumentException("Prompt cannot be empty.", nameof(prompt));

        try
        {
            var requestBody = new ChatGptRequest
            {
                Model = "gpt-4o-mini",
                Messages = new[]
                {
                    new ChatGptMessage { Role = "user", Content = prompt }
                }
            };

            var jsonBody = JsonSerializer.Serialize(requestBody);
            var content = new StringContent(jsonBody, Encoding.UTF8, "application/json");

            using var request = new HttpRequestMessage(HttpMethod.Post, BaseUrl);
            request.Headers.Add("Authorization", $"Bearer {_apiKey}");
            request.Content = content;

            _logger.LogInformation("Sending request to ChatGPT API");

            var response = await _httpClient.SendAsync(request);
            
            if (response.StatusCode == System.Net.HttpStatusCode.TooManyRequests)
            {
                _logger.LogWarning("ChatGPT API returned 429 (Rate Limit). Fallback will be used.");
                throw new InvalidOperationException("AI service communication failure - Rate limit reached");
            }
            
            if (response.StatusCode == System.Net.HttpStatusCode.ServiceUnavailable ||
                response.StatusCode == System.Net.HttpStatusCode.BadGateway ||
                response.StatusCode == System.Net.HttpStatusCode.GatewayTimeout)
            {
                _logger.LogWarning("ChatGPT API unavailable (Status: {StatusCode}). Fallback will be used.", response.StatusCode);
                throw new InvalidOperationException($"AI service communication failure - Service unavailable ({response.StatusCode})");
            }

            response.EnsureSuccessStatusCode();

            var responseBody = await response.Content.ReadAsStringAsync();
            var chatGptResponse = JsonSerializer.Deserialize<ChatGptResponse>(responseBody);

            var generatedText = chatGptResponse?.Choices?.FirstOrDefault()?.Message?.Content ?? string.Empty;
            
            _logger.LogInformation("Response received successfully from ChatGPT");
            
            return generatedText;
        }
        catch (TaskCanceledException ex) when (ex.InnerException is TimeoutException || _httpClient.Timeout < TimeSpan.FromMinutes(1))
        {
            _logger.LogWarning(ex, "Timeout on ChatGPT API request. Fallback will be used.");
            throw new InvalidOperationException("AI service communication failure - Timeout", ex);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogWarning(ex, "Network error communicating with ChatGPT API. Fallback will be used.");
            throw new InvalidOperationException("AI service communication failure - Network error", ex);
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Error processing ChatGPT JSON response");
            throw new InvalidOperationException("Error processing AI response", ex);
        }
        catch (InvalidOperationException)
        {
            throw;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error in ChatGptService. Fallback will be used.");
            throw new InvalidOperationException("AI service communication failure - Unexpected error", ex);
        }
    }
}
