# High-Copy Service - Template System Documentation

## Visão Geral

Este projeto implementa um sistema completo de templates para criação de briefs de marketing de alto valor. O sistema utiliza o Scriban Template Engine e segue uma arquitetura limpa para renderização de templates dinâmicos baseados em entidades do domínio.

## Arquitetura do Sistema

### Estrutura Principal
```
src/
├── Application/
│   ├── Templates/          # Templates Scriban (.sbn)
│   │   ├── campaign-setup.sbn
│   │   ├── expert-setup.sbn
│   │   ├── public-setup.sbn
│   │   └── product-setup.sbn
│   └── Prompts/Setups/     # Classes estáticas de renderização
│       ├── CampaignSetup.cs
│       ├── ExpertSetup.cs
│       ├── PublicSetup.cs
│       └── ProductSetup.cs
├── Domain/
│   ├── Entities/           # Entidades do domínio
│   │   ├── Campaign.cs
│   │   ├── Expert.cs
│   │   ├── Product.cs
│   │   └── Public.cs
│   └── Enums/             # Enumerações para templates
└── Tests/                 # Testes unitários completos
```

## Templates Implementados

### 1. Campaign Setup (campaign-setup.sbn)
**Propósito**: Brief interativo de campanha com framework de arquetipos

**Características**:
- Formato quiz interativo para engajamento
- 5 frameworks de arquetipos (Herói, Inocente, Sábio, Explorador, Rebelde)
- Níveis de consciência e sofisticação
- Framework "Batalha de Crenças"
- Renderização condicional baseada em enums

**Uso**:
```csharp
var prompt = CampaignSetup.CampaignSetupPrompt(campaignEntity);
```

### 2. Expert Setup (expert-setup.sbn)
**Propósito**: Setup detalhado para especialistas individuais e empresas

**Características**:
- 19 perguntas para especialistas individuais
- 17 perguntas para empresas
- Framework de autoridade e credibilidade
- Análise de mercado e posicionamento
- Renderização baseada no tipo de expert

**Uso**:
```csharp
// Para especialista individual
var prompt = ExpertSetup.ExpertSetupPrompt(expertEntity);

// Para empresa
var prompt = ExpertSetup.CompanySetupPrompt(expertEntity);

// Método universal
var prompt = ExpertSetup.GenerateSetupPrompt(expertEntity, "Expert|Company");
```

### 3. Public Setup (public-setup.sbn)
**Propósito**: Criação de avatar de audiência para vendas de alto valor

**Características**:
- Framework "Moment Catalyst" (momento catalisador)
- Hierarquia de dores identitárias
- Dog whistles psicológicos
- Checklists de validação
- Framework completo de high-ticket

**Uso**:
```csharp
var prompt = PublicSetup.PublicSetupPrompt(publicEntity);
```

### 4. Product Setup (product-setup.sbn)
**Propóstico**: Estrutura completa de oferta premium

**Características**:
- 11 seções de estruturação de oferta
- Promessa de transformação
- Provas sofisticadas
- Pilares metodológicos
- Framework de garantia
- Jornada do cliente otimizada

**Uso**:
```csharp
var prompt = ProductSetup.ProductSetupPrompt(productEntity);
```

## Entidades do Domínio

### Campaign Entity
```csharp
public class Campaign
{
    public string Name { get; set; }
    public string Goal { get; set; }
    public string TargetAudience { get; set; }
    public CampaignArchetypeEnum Archetype { get; set; }
    public CampaignConsciousnessEnum ConsciousnessLevel { get; set; }
    public CampaignSophisticationEnum SophisticationLevel { get; set; }
    public CampaignBeliefBattleEnum BeliefBattle { get; set; }
    public string Problem { get; set; }
    public string Solution { get; set; }
}
```

### Expert Entity
```csharp
public class Expert
{
    public string Name { get; set; }
    public string Specialization { get; set; }
    public string Experience { get; set; }
    public string Methodology { get; set; }
    public string Results { get; set; }
    public string Credentials { get; set; }
    public string Mission { get; set; }
    public string Vision { get; set; }
    public string Values { get; set; }
}
```

### Public Entity
```csharp
public class Public
{
    public string Name { get; set; }
    public string Demographics { get; set; }
    public string Psychographics { get; set; }
    public string PainPoints { get; set; }
    public string Desires { get; set; }
    public string Objections { get; set; }
    public string BuyingBehavior { get; set; }
    public string Triggers { get; set; }
}
```

### Product Entity
```csharp
public class Product
{
    public string Name { get; set; }
    public string Benefits { get; set; }
    public string SocialProof { get; set; }
    public string Metodology { get; set; }
    public string Price { get; set; }
    public string Guarantee { get; set; }
    public string Bonuses { get; set; }
    public ProductDeliverablesEnum Deliverables { get; set; }
    public ProductCustomerJourneyEnum CustomerJourney { get; set; }
}
```

## Enumerações Principais

### Campaign Enums
- `CampaignArchetypeEnum`: Herói, Inocente, Sábio, Explorador, Rebelde
- `CampaignConsciousnessEnum`: Inconsciente, Consciente, MuitoConsciente
- `CampaignSophisticationEnum`: Inexperiente, Experiente, Sofisticado
- `CampaignBeliefBattleEnum`: ProblemaBelief, SolutionBelief, SourceBelief

### Product Enums
- `ProductDeliverablesEnum`: Curso, Mentorias, Comunidade, Templates, Certificação
- `ProductCustomerJourneyEnum`: Discovery, Evaluation, Purchase, Onboarding, Success

## Engine de Templates

### Scriban Template Engine
- **Versão**: 6.2.1
- **Funcionalidades**: Renderização dinâmica, lógica condicional, loops
- **Sintaxe**: `{{ variable }}`, `{% if condition %}`, `{% for item %}`

### Estrutura de Renderização
1. **Carregamento**: Templates carregados do diretório Templates/
2. **Compilação**: Parsing via Scriban.Template.Parse()
3. **Rendering**: Aplicação de modelo de dados via template.Render()
4. **Fallback**: Sistema de caminhos alternativos para diferentes ambientes

## Testes Unitários

### Cobertura de Testes
- **11 testes implementados** - todos passando
- **4 arquivos de teste**:
  - `CampaignSetupTests.cs`
  - `ExpertSetupTests.cs` 
  - `PublicSetupTests.cs`
  - `ProductSetupTests.cs`

### Cenários Testados
1. **Renderização básica**: Verificação de output não-nulo
2. **Conteúdo específico**: Presença de elementos-chave nos templates
3. **Enums**: Mapeamento correto de valores de enumeração
4. **Condicionais**: Lógica de renderização baseada em tipos
5. **Fallbacks**: Caminhos alternativos de template

### Execução de Testes
```bash
dotnet test
# Resultado: 11 testes passando, 0 falhando
```

## Integração com API

### ContentController
```csharp
[ApiController]
[Route("api/[controller]")]
public class ContentController : ControllerBase
{
    [HttpPost("campaign-setup")]
    public IActionResult GenerateCampaignSetup([FromBody] Campaign campaign)
    {
        var prompt = CampaignSetup.CampaignSetupPrompt(campaign);
        return Ok(new { prompt });
    }
    
    // Endpoints similares para Expert, Public e Product
}
```

## Frameworks de Marketing Implementados

### 1. High-Ticket Marketing Framework
- **Moment Catalyst**: Identificação do momento de transformação
- **Identity Pain**: Dores relacionadas à identidade do prospect
- **Dog Whistles**: Sinais psicológicos de reconhecimento
- **Authority Positioning**: Posicionamento de autoridade especializada

### 2. Archetype Framework
- **5 Arquetipos**: Herói, Inocente, Sábio, Explorador, Rebelde
- **Aplicação**: Personalização de mensagem baseada no arquétipo
- **Contexto**: Campanhas de aquisição e retenção

### 3. Belief Battle Framework
- **Problem Belief**: Crença sobre o problema
- **Solution Belief**: Crença sobre a solução
- **Source Belief**: Crença sobre a fonte (autoridade)

### 4. Consciousness & Sophistication
- **Níveis de Consciência**: Inconsciente → Consciente → Muito Consciente
- **Níveis de Sofisticação**: Inexperiente → Experiente → Sofisticado
- **Aplicação**: Adequação de linguagem e abordagem

## Configuração e Deploy

### Dependências
```xml
<PackageReference Include="Scriban" Version="6.2.1" />
```

### Estrutura de Diretórios
- Templates devem estar em `Templates/` relativo ao assembly
- Fallback para diretório atual do projeto
- Paths absolutos calculados dinamicamente

### Ambiente de Desenvolvimento
```bash
# Clone do repositório
git clone [repository]

# Restaurar dependências
dotnet restore

# Build do projeto
dotnet build

# Executar testes
dotnet test

# Executar API
dotnet run --project src/Api
```

## Melhores Práticas Implementadas

### 1. Separação de Responsabilidades
- **Templates**: Apenas estrutura e apresentação
- **Setup Classes**: Lógica de mapeamento e renderização
- **Entities**: Modelo de dados limpo
- **Tests**: Validação abrangente

### 2. Flexibilidade
- **Enums**: Configuração tipada e extensível
- **Condicionais**: Renderização baseada em contexto
- **Fallbacks**: Robustez em diferentes ambientes

### 3. Manutenibilidade
- **Código limpo**: Remoção de comentários desnecessários
- **Testes**: Cobertura completa de funcionalidades
- **Documentação**: Guia completo de uso

### 4. Performance
- **Template Caching**: Templates compilados uma vez
- **Lazy Loading**: Carregamento sob demanda
- **Minimal Dependencies**: Apenas dependências necessárias

## Conclusão

O sistema de templates implementado oferece uma solução completa e profissional para geração de briefs de marketing de alto valor. Com arquitetura limpa, testes abrangentes e frameworks comprovados, o sistema está pronto para uso em produção e facilmente extensível para novos tipos de conteúdo.

**Status Final**: ✅ Sistema completo, testado e documentado
**Testes**: ✅ 11/11 passando
**Build**: ✅ Compilação limpa
**Qualidade**: ✅ Código profissional sem comentários desnecessários
