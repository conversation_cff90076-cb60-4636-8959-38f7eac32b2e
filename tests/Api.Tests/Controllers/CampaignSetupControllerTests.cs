using Api.Controllers;
using Api.Models;
using Domain.Entities;
using Domain.Interfaces.Services;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Api.Tests.Controllers;

public class CampaignSetupControllerTests
{
    private readonly Mock<ICampaignService> _mockService;
    private readonly Mock<ILogger<CampaignSetupController>> _mockLogger;
    private readonly CampaignSetupController _controller;

    public CampaignSetupControllerTests()
    {
        _mockService = new Mock<ICampaignService>();
        _mockLogger = new Mock<ILogger<CampaignSetupController>>();
        _controller = new CampaignSetupController(_mockService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturnOk_WhenSuccess()
    {
        var id = "camp-1";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(id, It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>()))
            .ReturnsAsync("TEMPLATE");

        var result = await _controller.GenerateSetupTemplate(id, null, null, null);

        var ok = result.Should().BeOfType<OkObjectResult>().Subject;
        var payload = ok.Value.Should().BeOfType<SetupTemplateResponse>().Subject;
        payload.Success.Should().BeTrue();
        payload.EntityType.Should().Be("Campaign");
        payload.Identifier.Should().Be(id);
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturnNotFound_WhenMissing()
    {
        var id = "missing";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(id, null, null, null))
            .ThrowsAsync(new KeyNotFoundException("not found"));

        var result = await _controller.GenerateSetupTemplate(id, null, null, null);

        var nf = result.Should().BeOfType<NotFoundObjectResult>().Subject;
        var payload = nf.Value.Should().BeOfType<SetupTemplateResponse>().Subject;
        payload.Success.Should().BeFalse();
        payload.Message.Should().Be("not found");
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturn500_OnUnhandledError()
    {
        var id = "err";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(id, null, null, null))
            .ThrowsAsync(new Exception("boom"));

        var result = await _controller.GenerateSetupTemplate(id, null, null, null);

        var obj = result.Should().BeOfType<ObjectResult>().Subject;
        obj.StatusCode.Should().Be(500);
    }

    [Fact]
    public async Task GetCampaign_ShouldReturnOk_WhenFound()
    {
        var id = "camp-1";
        var entity = new Campaign 
        { 
            Id = 1, 
            Name = "C1",
            Ideia = "Idea",
            Emotion = "Emotion",
            Belief = "Belief",
            Type = Domain.Enums.Campaing.CampaingTypeEnum.Launch,
            ConscienceLevel = Domain.Enums.Campaing.CampaingConscienceLevelEnum.TotallyUnconscious,
            SophisticationLevel = Domain.Enums.Campaing.CampaingSophisticationLevelEnum.FirstPresent
        };
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(id)).ReturnsAsync(entity);

        var result = await _controller.GetCampaign(id);

        var ok = result.Should().BeOfType<OkObjectResult>().Subject;
        ok.Value.Should().Be(entity);
    }

    [Fact]
    public async Task GetCampaign_ShouldReturnNotFound_WhenMissing()
    {
        var id = "missing";
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(id))
            .ThrowsAsync(new KeyNotFoundException());

        var result = await _controller.GetCampaign(id);

        result.Should().BeOfType<NotFoundObjectResult>();
    }

    [Fact]
    public async Task GetCampaign_ShouldReturn500_OnUnhandledError()
    {
        var id = "err";
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(id))
            .ThrowsAsync(new Exception("boom"));

        var result = await _controller.GetCampaign(id);

        var obj = result.Should().BeOfType<ObjectResult>().Subject;
        obj.StatusCode.Should().Be(500);
    }
}


