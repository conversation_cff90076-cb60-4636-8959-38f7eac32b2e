using Api.Controllers;
using Api.Models;
using Domain.Entities;
using Domain.Interfaces.Services;
using FluentAssertions;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Xunit;

namespace Api.Tests.Controllers;

public class ProductSetupControllerTests
{
    private readonly Mock<IProductService> _mockService;
    private readonly Mock<ILogger<ProductSetupController>> _mockLogger;
    private readonly ProductSetupController _controller;

    public ProductSetupControllerTests()
    {
        _mockService = new Mock<IProductService>();
        _mockLogger = new Mock<ILogger<ProductSetupController>>();
        _controller = new ProductSetupController(_mockService.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturnOk_WhenSuccess()
    {
        var id = "prod-1";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(id))
            .ReturnsAsync("TEMPLATE");

        var result = await _controller.GenerateSetupTemplate(id);

        var ok = result.Should().BeOfType<OkObjectResult>().Subject;
        var payload = ok.Value.Should().BeOfType<SetupTemplateResponse>().Subject;
        payload.Success.Should().BeTrue();
        payload.EntityType.Should().Be("Product");
        payload.Identifier.Should().Be(id);
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturnNotFound_WhenMissing()
    {
        var id = "missing";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(id))
            .ThrowsAsync(new KeyNotFoundException("not found"));

        var result = await _controller.GenerateSetupTemplate(id);

        var nf = result.Should().BeOfType<NotFoundObjectResult>().Subject;
        var payload = nf.Value.Should().BeOfType<SetupTemplateResponse>().Subject;
        payload.Success.Should().BeFalse();
        payload.Message.Should().Be("not found");
    }

    [Fact]
    public async Task GenerateSetupTemplate_ShouldReturn500_OnUnhandledError()
    {
        var id = "err";
        _mockService.Setup(s => s.GenerateSetupTemplateAsync(id))
            .ThrowsAsync(new Exception("boom"));

        var result = await _controller.GenerateSetupTemplate(id);

        var obj = result.Should().BeOfType<ObjectResult>().Subject;
        obj.StatusCode.Should().Be(500);
    }

    [Fact]
    public async Task GetProduct_ShouldReturnOk_WhenFound()
    {
        var id = "prod-1";
        var entity = new Product 
        { 
            Id = 1, 
            Name = "P1",
            Benefits = "Benefits",
            SocialProof = "Proof",
            Metodology = "Method",
            Guarantee = "Guarantee",
            Deliverables = Domain.Enums.Products.ProductDeliverablesEnum.Others,
            CustomerJourney = Domain.Enums.Products.ProductCustomerJourneyEnum.OneTwoMonths
        };
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(id)).ReturnsAsync(entity);

        var result = await _controller.GetProduct(id);

        var ok = result.Should().BeOfType<OkObjectResult>().Subject;
        ok.Value.Should().Be(entity);
    }

    [Fact]
    public async Task GetProduct_ShouldReturnNotFound_WhenMissing()
    {
        var id = "missing";
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(id))
            .ThrowsAsync(new KeyNotFoundException());

        var result = await _controller.GetProduct(id);

        result.Should().BeOfType<NotFoundObjectResult>();
    }

    [Fact]
    public async Task GetProduct_ShouldReturn500_OnUnhandledError()
    {
        var id = "err";
        _mockService.Setup(s => s.GetByIdOrExternalIdAsync(id))
            .ThrowsAsync(new Exception("boom"));

        var result = await _controller.GetProduct(id);

        var obj = result.Should().BeOfType<ObjectResult>().Subject;
        obj.StatusCode.Should().Be(500);
    }
}


