using Application.Prompts.Setups;
using Domain.Entities;
using Domain.Enums.Products;
using Xunit;

namespace Application.Tests.Prompts.Setups
{
    public class ProductSetupTests
    {
        [Fact]
        public void ProductSetupPrompt_ShouldRenderTemplateWithProductData()
        {
            // Arrange
            var product = new Product
            {
                Name = "Mentoria Elite - Escala High Ticket",
                Benefits = "1. Faturamento 5x maior em 6 meses; 2. Sistema de vendas automatizado; 3. Margem de lucro 80%+; 4. Time independente funcionando; 5. Posicionamento premium no mercado",
                SocialProof = "347 empresários formados, ticket médio de R$ 2,3M por aluno, 89% de taxa de sucesso comprovada por auditoria Ernst & Young",
                Metodology = "Metodologia ESCALA: Simplificação Radical + Produtização + Alavancagem Assimétrica para multiplicar resultados sem multiplicar esforço",
                Guarantee = "Garantia Dupla: 60 dias incondicional + Se não aumentar faturamento em 100% em 6 meses, devolvemos tudo e você fica com o material",
                Deliverables = ProductDeliverablesEnum.SessionMentoring,
                CustomerJourney = ProductCustomerJourneyEnum.SevenTwelveMonths
            };

            // Act
            var result = ProductSetup.ProductSetupPrompt(product);

            // Assert
            Assert.NotNull(result);
            Assert.NotEmpty(result);
            
            // Verificar se os dados do produto estão no resultado
            Assert.Contains("Mentoria Elite - Escala High Ticket", result);
            Assert.Contains("Faturamento 5x maior", result);
            Assert.Contains("347 empresários formados", result);
            Assert.Contains("Metodologia ESCALA", result);
            Assert.Contains("Garantia Dupla", result);
            
            // Verificar estrutura do template
            Assert.Contains("BRIEFING DE PRODUTO/OFERTA", result);
            Assert.Contains("IDENTIFICAÇÃO PRINCIPAL", result);
            Assert.Contains("A PROMESSA PRINCIPAL DA TRANSFORMAÇÃO", result);
            Assert.Contains("BENEFÍCIOS CHAVE A SEREM COMUNICADOS", result);
            Assert.Contains("PROVAS E ARGUMENTOS", result);
            Assert.Contains("A METODOLOGIA (Os Pilares da Transformação)", result);
            Assert.Contains("OS ENTREGÁVEIS", result);
            Assert.Contains("A JORNADA DO CLIENTE", result);
            Assert.Contains("OS BÔNUS ESTRATÉGICOS", result);
            Assert.Contains("A GARANTIA", result);
            Assert.Contains("A ESCASSEZ", result);
            
            // Verificar se não há variáveis não renderizadas
            Assert.DoesNotContain("{{", result);
            Assert.DoesNotContain("}}", result);
        }

        [Fact]
        public void ProductSetupPrompt_ShouldMapAllEntityPropertiesCorrectly()
        {
            // Arrange
            var product = new Product
            {
                Name = "TestProduct",
                Benefits = "TestBenefits",
                SocialProof = "TestSocialProof",
                Metodology = "TestMethodology",
                Guarantee = "TestGuarantee",
                Deliverables = ProductDeliverablesEnum.FollowUpSuport,
                CustomerJourney = ProductCustomerJourneyEnum.ThreSixMonths
            };

            // Act
            var result = ProductSetup.ProductSetupPrompt(product);

            // Assert
            // Verificar se todas as propriedades da entidade estão sendo mapeadas
            Assert.Contains("TestProduct", result);
            Assert.Contains("TestBenefits", result);
            Assert.Contains("TestSocialProof", result);
            Assert.Contains("TestMethodology", result);
            Assert.Contains("TestGuarantee", result);
            Assert.Contains("FollowUpSuport", result);
            Assert.Contains("ThreSixMonths", result);
        }

        [Fact]
        public void ProductSetupPrompt_ShouldIncludeHighTicketFrameworkElements()
        {
            // Arrange
            var product = new Product
            {
                Name = "Premium Product",
                Benefits = "Premium Benefits",
                SocialProof = "Premium Social Proof",
                Metodology = "Premium Methodology",
                Guarantee = "Premium Guarantee",
                Deliverables = ProductDeliverablesEnum.MeetingsInPerson,
                CustomerJourney = ProductCustomerJourneyEnum.OneTwoMonths
            };

            // Act
            var result = ProductSetup.ProductSetupPrompt(product);

            // Assert
            // Verificar elementos específicos do framework high ticket
            Assert.Contains("PROVA SOFISTICADA (DEMONSTRA valor)", result);
            Assert.Contains("OS PILARES FUNDAMENTAIS", result);
            Assert.Contains("MECANISMO ÚNICO", result);
            Assert.Contains("ESCASSEZ GENUÍNA", result);
            Assert.Contains("A Inversão Total do Risco", result);
            Assert.Contains("FRAMEWORK DE OFERTA HIGH TICKET", result);
        }

        [Fact]
        public void ProductSetupPrompt_ShouldIncludeCustomerJourneyBasedOnEnum()
        {
            // Arrange - Teste com jornada de 7-12 meses
            var product = new Product
            {
                Name = "Long Term Product",
                Benefits = "Long term benefits",
                SocialProof = "Long term proof",
                Metodology = "Long term methodology",
                Guarantee = "Long term guarantee",
                Deliverables = ProductDeliverablesEnum.Others,
                CustomerJourney = ProductCustomerJourneyEnum.SevenTwelveMonths
            };

            // Act
            var result = ProductSetup.ProductSetupPrompt(product);

            // Assert
            Assert.Contains("PROGRAMA DE 7-12 MESES", result);
            Assert.Contains("Meses 1-2 (A Fundação)", result);
            Assert.Contains("Meses 3-6 (A Tração)", result);
            Assert.Contains("Meses 7-12 (A Aceleração)", result);
        }

        [Fact]
        public void ProductSetupPrompt_ShouldIncludeDeliverablesBasedOnEnum()
        {
            // Arrange - Teste com diferentes tipos de entregáveis
            var product = new Product
            {
                Name = "Mentoring Product",
                Benefits = "Mentoring benefits",
                SocialProof = "Mentoring proof",
                Metodology = "Mentoring methodology",
                Guarantee = "Mentoring guarantee",
                Deliverables = ProductDeliverablesEnum.SessionMentoring,
                CustomerJourney = ProductCustomerJourneyEnum.ThreSixMonths
            };

            // Act
            var result = ProductSetup.ProductSetupPrompt(product);

            // Assert
            Assert.Contains("SESSÕES DE MENTORIA", result);
            Assert.Contains("Quantidade:", result);
            Assert.Contains("Duração:", result);
            Assert.Contains("Frequência:", result);
        }

        [Fact]
        public void ProductSetupPrompt_ShouldIncludeValidationChecklist()
        {
            // Arrange
            var product = new Product
            {
                Name = "Validation Product",
                Benefits = "Validation benefits",
                SocialProof = "Validation proof",
                Metodology = "Validation methodology",
                Guarantee = "Validation guarantee",
                Deliverables = ProductDeliverablesEnum.Others,
                CustomerJourney = ProductCustomerJourneyEnum.OneTwoMonths
            };

            // Act
            var result = ProductSetup.ProductSetupPrompt(product);

            // Assert
            // Verificar se inclui elementos de validação e próximos passos
            Assert.Contains("VALIDAÇÃO DA OFERTA", result);
            Assert.Contains("PRÓXIMOS PASSOS", result);
            Assert.Contains("ITENS PARA COMPLEMENTAR", result);
            Assert.Contains("FRAMEWORK DE APLICAÇÃO", result);
            
            // Verificar checklist de validação
            Assert.Contains("Confirmar se a promessa é específica e mensurável", result);
            Assert.Contains("Validar se as provas são suficientemente sofisticadas", result);
            Assert.Contains("Testar se a metodologia é única e defensável", result);
        }
    }
}
